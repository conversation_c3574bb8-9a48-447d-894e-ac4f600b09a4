# Deployment Guide for Vercel

## Prerequisites

1. **Spotify App Setup**:
   - Go to https://developer.spotify.com/dashboard/
   - Create a new app
   - Add redirect URI: `http://localhost:8888/callback`
   - Note your Client ID and Client Secret

2. **Environment Variables**:
   - Copy `.env.local.example` to `.env.local`
   - Fill in your Spotify credentials

## Local Setup

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Run Spotify setup**:
   ```bash
   npm run setup-spotify
   ```

3. **Start development server**:
   ```bash
   npm run dev
   ```

## Vercel Deployment

1. **Connect to Vercel**:
   - Install Vercel CLI: `npm i -g vercel`
   - Run `vercel` in your project directory
   - Follow the prompts to connect your GitHub repo

2. **Set Environment Variables in Vercel**:
   - Go to your Vercel project dashboard
   - Navigate to Settings > Environment Variables
   - Add the following variables:
     - `SPOTIFY_CLIENT_ID`: Your Spotify Client ID
     - `SPOTIFY_CLIENT_SECRET`: Your Spotify Client Secret
     - `SPOTIFY_REFRESH_TOKEN`: Generated from setup script

3. **Deploy**:
   ```bash
   vercel --prod
   ```

## Domain Configuration

Your site will be available at: `https://ourudev-omega.vercel.app/`

The Spotify integration will automatically work with:
- API endpoint: `https://ourudev-omega.vercel.app/api/spotify`
- Proper CORS configuration for your domain

## Troubleshooting

1. **Spotify API Issues**:
   - Ensure all environment variables are set in Vercel
   - Check that your refresh token is valid
   - Verify redirect URI matches exactly in Spotify Dashboard

2. **Build Issues**:
   - Make sure all dependencies are installed
   - Check that TypeScript files compile correctly

3. **API Endpoint Issues**:
   - Verify the `/api/spotify.js` file is in the correct location
   - Check Vercel function logs for errors

## Features

- ✅ Spotify integration with real-time track display
- ✅ Responsive design
- ✅ Dark/light theme support
- ✅ Serverless API functions
- ✅ Automatic token refresh
- ✅ Caching for performance
